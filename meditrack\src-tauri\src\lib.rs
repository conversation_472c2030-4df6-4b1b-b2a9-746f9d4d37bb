mod ipc;
mod state;

use state::AppState;
use tracing::{error, info};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

// Learn more about Tauri commands at https://tauri.app/v1/guides/features/command
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

/// Initialize logging for the application
fn init_logging() {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "medexphermo=debug,db_service=debug,info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();
}

/// Setup the application state and handle initialization errors
async fn setup_app_state() -> Result<AppState, Box<dyn std::error::Error + Send + Sync>> {
    info!("Setting up MedexPhermo application state...");

    match AppState::new().await {
        Ok(state) => {
            info!("Application state initialized successfully");
            Ok(state)
        }
        Err(e) => {
            error!("Failed to initialize application state: {}", e);
            Err(Box::new(e))
        }
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize logging first
    init_logging();
    info!("Starting MedexPhermo application...");

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            // Initialize app state asynchronously
            let handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                match setup_app_state().await {
                    Ok(state) => {
                        handle.manage(state);
                        info!("Application setup completed successfully");
                    }
                    Err(e) => {
                        error!("Application setup failed: {}", e);
                        std::process::exit(1);
                    }
                }
            });
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
